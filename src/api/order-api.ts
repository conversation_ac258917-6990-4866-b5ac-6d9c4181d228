import { OrderEntity } from "@/core.constants";
import { firestore, firebaseFunctions } from "@/root-context";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  DocumentSnapshot,
  getCountFromServer,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  updateDoc,
  where,
  writeBatch,
} from "firebase/firestore";
import { httpsCallable } from "firebase/functions";

const COLLECTION_NAME = "orders";

// Interfaces for order filtering and pagination
export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: "price_asc" | "price_desc" | "date_asc" | "date_desc";
  limit?: number;
  lastDoc?: DocumentSnapshot | null; // DocumentSnapshot for pagination
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null; // DocumentSnapshot for next page
  hasMore: boolean;
}

/**
 * Caching system for order queries with pagination support
 *
 * Features:
 * - Caches order queries for 2 minutes to reduce Firebase reads
 * - Smart pagination caching: stores all fetched pages for a filter set
 * - Automatically invalidates cache on create/update/delete operations
 * - Separate cache for buyers and sellers queries
 * - Accumulates pages instead of caching each page separately
 */
interface CachedOrderPages {
  pages: OrderEntity[][];
  lastDocs: (DocumentSnapshot | null)[];
  hasMore: boolean;
  timestamp: number;
  totalFetched: number;
}

const ordersCache = new Map<string, CachedOrderPages>();
const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes in milliseconds

// Cache management functions
const isCacheValid = (timestamp: number): boolean => {
  return Date.now() - timestamp < CACHE_DURATION;
};

const clearCache = () => {
  ordersCache.clear();
};

const generateCacheKey = (
  functionName: string,
  filters: OrderFilters
): string => {
  // Create cache key based on function name and filters (excluding pagination)
  // This allows us to cache all pages for the same filter set together
  const safeFilters = {
    minPrice: typeof filters.minPrice === "number" ? filters.minPrice : null,
    maxPrice: typeof filters.maxPrice === "number" ? filters.maxPrice : null,
    collectionId:
      typeof filters.collectionId === "string" ? filters.collectionId : null,
    sortBy:
      typeof filters.sortBy === "string" &&
      ["price_asc", "price_desc", "date_asc", "date_desc"].includes(
        filters.sortBy
      )
        ? filters.sortBy
        : "date_desc",
    limit: typeof filters.limit === "number" ? filters.limit : null,
    // Don't include lastDoc in cache key - we want same key for all pages of same filter
  };

  try {
    const filterKey = JSON.stringify(safeFilters);
    return `${functionName}:${filterKey}`;
  } catch (error) {
    console.error(
      "Error generating cache key:",
      error,
      "filters:",
      filters,
      "safeFilters:",
      safeFilters
    );
    // Fallback to a simple key without problematic values
    return `${functionName}:fallback:${Date.now()}`;
  }
};

const getCachedResult = (
  functionName: string,
  filters: OrderFilters
): PaginatedOrdersResult | null => {
  const cacheKey = generateCacheKey(functionName, filters);
  const cached = ordersCache.get(cacheKey);

  if (!cached || !isCacheValid(cached.timestamp)) {
    // Remove expired cache entry
    if (cached) {
      ordersCache.delete(cacheKey);
    }
    return null;
  }

  // Determine which page is being requested
  let requestedPage = 0;
  if (filters.lastDoc) {
    console.log(
      `🔍 Looking for lastDoc ${filters.lastDoc?.id} in cache with ${cached.lastDocs.length} pages`
    );
    console.log(
      `🔍 Cached lastDocs:`,
      cached.lastDocs.map((doc) => doc?.id || "null")
    );

    // Find which page this lastDoc belongs to
    const lastDocIndex = cached.lastDocs.findIndex(
      (doc) => doc?.id === filters.lastDoc?.id
    );
    console.log(`🔍 Found lastDoc at index: ${lastDocIndex}`);

    if (lastDocIndex >= 0) {
      // We want the page AFTER the one that ends with this lastDoc
      requestedPage = lastDocIndex + 1;
      console.log(
        `🔍 Requesting page ${requestedPage} (after page ${lastDocIndex})`
      );
    } else {
      // lastDoc not found in cache, this means we need to fetch from Firebase
      console.log(
        `🔍 lastDoc ${filters.lastDoc?.id} not found in cache, need to fetch from Firebase`
      );
      return null;
    }
  } else {
    console.log(`🔍 No lastDoc provided, requesting page 0`);
  }

  // Check if we have the requested page
  if (requestedPage < cached.pages.length) {
    console.log(
      `📄 Returning cached page ${requestedPage} for ${functionName}`
    );

    // Calculate if there are more pages after this one
    const hasMore = requestedPage + 1 < cached.pages.length || cached.hasMore;

    return {
      orders: cached.pages[requestedPage],
      lastDoc: cached.lastDocs[requestedPage],
      hasMore,
    };
  }

  console.log(
    `🔍 Page ${requestedPage} not in cache (have ${cached.pages.length} pages), need to fetch from Firebase`
  );

  return null;
};

const setCachedResult = (
  functionName: string,
  filters: OrderFilters,
  data: PaginatedOrdersResult
): void => {
  const cacheKey = generateCacheKey(functionName, filters);

  let cached = ordersCache.get(cacheKey);

  if (!cached) {
    // First page - create new cache entry
    cached = {
      pages: [data.orders],
      lastDocs: [data.lastDoc],
      hasMore: data.hasMore,
      timestamp: Date.now(),
      totalFetched: data.orders.length,
    };
  } else {
    // Subsequent page - append to existing cache
    if (filters.lastDoc) {
      // This is a pagination request - append the new page
      const lastDocIndex = cached.lastDocs.findIndex(
        (doc) => doc?.id === filters.lastDoc?.id
      );
      if (lastDocIndex >= 0) {
        // We found the lastDoc, so this should be the next page
        const nextPageIndex = lastDocIndex + 1;
        if (nextPageIndex >= cached.pages.length) {
          // This is indeed a new page, add it
          cached.pages.push(data.orders);
          cached.lastDocs.push(data.lastDoc);
          cached.hasMore = data.hasMore;
          cached.totalFetched += data.orders.length;
          cached.timestamp = Date.now();
          console.log(
            `📝 Added page ${nextPageIndex} to cache for ${functionName}`
          );
        }
      }
    } else {
      // This is a reset request (lastDoc is null), replace the cache
      cached.pages = [data.orders];
      cached.lastDocs = [data.lastDoc];
      cached.hasMore = data.hasMore;
      cached.totalFetched = data.orders.length;
      cached.timestamp = Date.now();
      console.log(`🔄 Reset cache for ${functionName}`);
    }
  }

  ordersCache.set(cacheKey, cached);
};

export const createOrder = async (
  orderData: Omit<OrderEntity, "id" | "createdAt" | "updatedAt">
) => {
  try {
    // Filter out undefined values to avoid Firestore errors
    const cleanOrderData = Object.fromEntries(
      Object.entries(orderData).filter(([, value]) => value !== undefined)
    );

    const docRef = await addDoc(collection(firestore, COLLECTION_NAME), {
      ...cleanOrderData,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Clear cache after creating order
    clearCache();

    return docRef.id;
  } catch (error) {
    console.error("Error creating order:", error);
    throw error;
  }
};

export const updateOrder = async (
  id: string,
  orderData: Partial<OrderEntity>
) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...orderData,
      updatedAt: new Date(),
    });

    // Clear cache after updating order
    clearCache();
  } catch (error) {
    console.error("Error updating order:", error);
    throw error;
  }
};

export const deleteOrder = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);

    // Clear cache after deleting order
    clearCache();
  } catch (error) {
    console.error("Error deleting order:", error);
    throw error;
  }
};

export const getOrders = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot
) => {
  try {
    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy("createdAt", "desc"),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy("createdAt", "desc"),
        startAfter(lastDoc),
        limit(pageSize)
      );
    }

    const snapshot = await getDocs(q);
    const orders: (OrderEntity & { id: string })[] = [];

    snapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity & {
        id: string;
      });
    });

    return {
      orders,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error("Error fetching orders:", error);
    throw error;
  }
};

export const clearAllOrders = async () => {
  try {
    const snapshot = await getDocs(collection(firestore, COLLECTION_NAME));
    const batch = writeBatch(firestore);

    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Deleted ${snapshot.docs.length} orders`);

    // Clear cache after bulk delete
    clearCache();
  } catch (error) {
    console.error("Error clearing orders:", error);
    throw error;
  }
};

export const createBulkOrders = async (
  orders: Omit<OrderEntity, "id" | "createdAt" | "updatedAt">[]
) => {
  try {
    const batch = writeBatch(firestore);
    const orderCollection = collection(firestore, COLLECTION_NAME);

    orders.forEach((orderData) => {
      const docRef = doc(orderCollection);

      // Filter out undefined values to avoid Firestore errors
      const cleanOrderData = Object.fromEntries(
        Object.entries(orderData).filter(([, value]) => value !== undefined)
      );

      batch.set(docRef, {
        ...cleanOrderData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    });

    await batch.commit();
    console.log(`Created ${orders.length} orders`);

    // Clear cache after bulk create
    clearCache();
  } catch (error) {
    console.error("Error creating bulk orders:", error);
    throw error;
  }
};

export const getPaidOrdersCount = async (): Promise<number> => {
  try {
    const q = query(
      collection(firestore, COLLECTION_NAME),
      where("status", "==", "paid")
    );

    const snapshot = await getCountFromServer(q);
    return snapshot.data().count;
  } catch (error) {
    console.error("Error getting paid orders count:", error);
    throw error;
  }
};

export const getOrdersForSellers = async (
  filters: OrderFilters = {}
): Promise<PaginatedOrdersResult> => {
  try {
    // Check cache first (including pagination)
    const cachedResult = getCachedResult("getOrdersForSellers", filters);
    if (cachedResult) {
      console.log("🎯 Returning cached result for getOrdersForSellers");
      return cachedResult;
    }

    const pageSize = filters.limit || 20;
    console.log("🔍 getOrdersForSellers called with filters:", filters);

    // For sellers tab: get orders where buyerId exists and sellerId is null/doesn't exist (buyers looking for sellers)
    let q = query(
      collection(firestore, COLLECTION_NAME),
      where("status", "==", "active"),
      where("buyerId", "!=", null)
    );

    if (filters.collectionId) {
      q = query(q, where("collectionId", "==", filters.collectionId));
    }

    // Apply sorting
    if (filters.sortBy) {
      if (filters.sortBy === "price_asc") {
        q = query(q, orderBy("amount", "asc"));
      } else if (filters.sortBy === "price_desc") {
        q = query(q, orderBy("amount", "desc"));
      } else if (filters.sortBy === "date_asc") {
        q = query(q, orderBy("createdAt", "asc"));
      } else {
        q = query(q, orderBy("createdAt", "desc"));
      }
    } else {
      q = query(q, orderBy("createdAt", "desc"));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }
    q = query(q, limit(pageSize + 1)); // Get one extra to check if there are more

    const snapshot = await getDocs(q);
    console.log("📊 Raw query returned", snapshot.docs.length, "documents");
    let orders: OrderEntity[] = [];
    let lastDoc = null;
    let hasMore = false;

    const docs = snapshot.docs;
    let processedCount = 0;

    for (let index = 0; index < docs.length; index++) {
      const doc = docs[index];

      if (processedCount >= pageSize) {
        hasMore = true; // There's at least one more document
        break;
      }

      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;
      console.log("📋 Order data:", {
        id: orderData.id,
        buyerId: orderData.buyerId,
        sellerId: orderData.sellerId,
        status: orderData.status,
      });

      // For sellers tab: only include orders where sellerId is null or doesn't exist
      if (!orderData.sellerId) {
        orders.push(orderData);
        lastDoc = doc; // Only set lastDoc when we actually include an order
        processedCount++;
        console.log("✅ Order included for sellers tab");
      } else {
        console.log("❌ Order excluded (has sellerId)");
      }
    }

    // Apply price filters client-side
    if (filters.minPrice !== undefined) {
      orders = orders.filter((order) => order.amount >= filters.minPrice!);
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter((order) => order.amount <= filters.maxPrice!);
    }

    console.log("🎯 Final result for sellers:", {
      ordersCount: orders.length,
      hasMore,
      lastDoc: !!lastDoc,
    });

    const result = {
      orders,
      lastDoc,
      hasMore,
    };

    // Cache the result (including pagination)
    setCachedResult("getOrdersForSellers", filters, result);

    return result;
  } catch (error) {
    console.error("Error fetching orders for sellers:", error);
    throw error;
  }
};

export const getOrdersForBuyers = async (
  filters: OrderFilters = {}
): Promise<PaginatedOrdersResult> => {
  try {
    // Check cache first (including pagination)
    const cachedResult = getCachedResult("getOrdersForBuyers", filters);
    if (cachedResult) {
      console.log("🎯 Returning cached result for getOrdersForBuyers");
      return cachedResult;
    }

    const pageSize = filters.limit || 20;
    console.log("🔍 getOrdersForBuyers called with filters:", filters);

    // For buyers tab: get orders where sellerId exists and buyerId is null/doesn't exist (sellers looking for buyers)
    let q = query(
      collection(firestore, COLLECTION_NAME),
      where("status", "==", "active"),
      where("sellerId", "!=", null)
    );

    if (filters.collectionId) {
      q = query(q, where("collectionId", "==", filters.collectionId));
    }

    // Apply sorting
    if (filters.sortBy) {
      if (filters.sortBy === "price_asc") {
        q = query(q, orderBy("amount", "asc"));
      } else if (filters.sortBy === "price_desc") {
        q = query(q, orderBy("amount", "desc"));
      } else if (filters.sortBy === "date_asc") {
        q = query(q, orderBy("createdAt", "asc"));
      } else {
        q = query(q, orderBy("createdAt", "desc"));
      }
    } else {
      q = query(q, orderBy("createdAt", "desc"));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }
    q = query(q, limit(pageSize + 1)); // Get one extra to check if there are more

    const snapshot = await getDocs(q);
    let orders: OrderEntity[] = [];
    let lastDoc = null;
    let hasMore = false;

    const docs = snapshot.docs;
    let processedCount = 0;

    for (let index = 0; index < docs.length; index++) {
      const doc = docs[index];

      if (processedCount >= pageSize) {
        hasMore = true; // There's at least one more document
        break;
      }

      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;

      // For buyers tab: only include orders where buyerId is null or doesn't exist
      if (!orderData.buyerId) {
        orders.push(orderData);
        lastDoc = doc; // Only set lastDoc when we actually include an order
        processedCount++;
      }
    }

    // Apply price filters client-side
    if (filters.minPrice !== undefined) {
      orders = orders.filter((order) => order.amount >= filters.minPrice!);
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter((order) => order.amount <= filters.maxPrice!);
    }

    const result = {
      orders,
      lastDoc,
      hasMore,
    };

    // Cache the result (including pagination)
    setCachedResult("getOrdersForBuyers", filters, result);

    return result;
  } catch (error) {
    console.error("Error fetching orders for buyers:", error);
    throw error;
  }
};

// Debug function to see all orders in the database
export const debugAllOrders = async (): Promise<void> => {
  try {
    console.log("🔍 DEBUG: Fetching all orders from database...");
    const q = query(collection(firestore, COLLECTION_NAME));
    const snapshot = await getDocs(q);

    console.log(`📊 Total orders in database: ${snapshot.docs.length}`);

    snapshot.docs.forEach((doc, index) => {
      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;
      console.log(`📋 Order ${index + 1}:`, {
        id: orderData.id,
        buyerId: orderData.buyerId,
        sellerId: orderData.sellerId,
        status: orderData.status,
        amount: orderData.amount,
        collectionId: orderData.collectionId,
        createdAt: orderData.createdAt,
      });
    });
  } catch (error) {
    console.error("Error fetching all orders:", error);
  }
};

// Secondary Market Functions

export interface SetSecondaryMarketPriceResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
}

export interface MakeSecondaryMarketPurchaseResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

/**
 * Set secondary market price for a PAID order (only current buyer can call this)
 */
export const setSecondaryMarketPrice = async (
  orderId: string,
  secondaryMarketPrice: number
): Promise<SetSecondaryMarketPriceResponse> => {
  try {
    const setSecondaryMarketPriceFunction = httpsCallable<
      { orderId: string; secondaryMarketPrice: number },
      SetSecondaryMarketPriceResponse
    >(firebaseFunctions, "setSecondaryMarketPrice");

    const result = await setSecondaryMarketPriceFunction({
      orderId,
      secondaryMarketPrice,
    });

    // Clear cache after setting secondary market price
    clearCache();

    return result.data;
  } catch (error) {
    console.error("Error setting secondary market price:", error);
    throw error;
  }
};

/**
 * Purchase an order from secondary market
 */
export const makeSecondaryMarketPurchase = async (
  orderId: string
): Promise<MakeSecondaryMarketPurchaseResponse> => {
  try {
    const makeSecondaryMarketPurchaseFunction = httpsCallable<
      { orderId: string },
      MakeSecondaryMarketPurchaseResponse
    >(firebaseFunctions, "makeSecondaryMarketPurchase");

    const result = await makeSecondaryMarketPurchaseFunction({ orderId });

    // Clear cache after secondary market purchase
    clearCache();

    return result.data;
  } catch (error) {
    console.error("Error making secondary market purchase:", error);
    throw error;
  }
};

/**
 * Get orders available on secondary market (orders with secondaryMarketPrice set)
 */
export const getSecondaryMarketOrders = async (
  filters: OrderFilters = {}
): Promise<PaginatedOrdersResult> => {
  try {
    // Check cache first (including pagination)
    const cachedResult = getCachedResult("getSecondaryMarketOrders", filters);
    if (cachedResult) {
      console.log("🎯 Returning cached result for getSecondaryMarketOrders");
      return cachedResult;
    }

    const pageSize = filters.limit ?? 20;
    console.log("🔍 getSecondaryMarketOrders called with filters:", filters);

    // Get orders with secondary market price set and PAID status
    let q = query(
      collection(firestore, COLLECTION_NAME),
      where("status", "==", "paid"),
      where("secondaryMarketPrice", "!=", null)
    );

    if (filters.collectionId) {
      q = query(q, where("collectionId", "==", filters.collectionId));
    }

    // Apply sorting
    if (filters.sortBy) {
      if (filters.sortBy === "price_asc") {
        q = query(q, orderBy("secondaryMarketPrice", "asc"));
      } else if (filters.sortBy === "price_desc") {
        q = query(q, orderBy("secondaryMarketPrice", "desc"));
      } else if (filters.sortBy === "date_asc") {
        q = query(q, orderBy("createdAt", "asc"));
      } else {
        q = query(q, orderBy("createdAt", "desc"));
      }
    } else {
      q = query(q, orderBy("createdAt", "desc"));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }

    q = query(q, limit(pageSize + 1)); // Fetch one extra to check if there are more

    const snapshot = await getDocs(q);
    const docs = snapshot.docs;
    let hasMore = false;

    if (docs.length > pageSize) {
      hasMore = true;
      docs.pop(); // Remove the extra document
    }

    let orders: OrderEntity[] = docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as OrderEntity[];

    // Apply price filters client-side (using secondaryMarketPrice)
    if (filters.minPrice !== undefined) {
      orders = orders.filter(
        (order) =>
          order.secondaryMarketPrice &&
          order.secondaryMarketPrice >= filters.minPrice!
      );
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter(
        (order) =>
          order.secondaryMarketPrice &&
          order.secondaryMarketPrice <= filters.maxPrice!
      );
    }

    const result = {
      orders,
      lastDoc: docs.length > 0 ? docs[docs.length - 1] : null,
      hasMore,
    };

    // Cache the result (including pagination)
    setCachedResult("getSecondaryMarketOrders", filters, result);

    return result;
  } catch (error) {
    console.error("Error fetching secondary market orders:", error);
    throw error;
  }
};

/**
 * Public function to manually clear the orders cache
 */
export const clearOrdersCache = () => {
  clearCache();
};
